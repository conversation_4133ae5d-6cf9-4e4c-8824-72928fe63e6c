using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IMembershipDal:IEntityRepository<Membership>
    {
        MembershipType GetMembershipType(int membershipTypeId);
        List<MembershipFreezeDto> GetFrozenMemberships();
        void FreezeMembership(int membershipId, int freezeDays);
        void UnfreezeMembership(int membershipId);
        bool IsMembershipFrozen(int membershipId);
        int GetRemainingFreezeDays(int membershipId);
        void CancelFreeze(int membershipId);
        void ReactivateFromToday(int membershipId);
        List<MembershipDetailForDeleteDto> GetMemberActiveMemberships(int memberId);
        IResult AddMembershipWithPaymentAndDebt(MembershipAddDto membershipDto);
        IResult DeleteMembershipWithRelatedData(int id, int companyId);
        IResult UpdateMembershipWithDateManagement(MembershipUpdateDto membershipDto, int companyId);
        IDataResult<LastMembershipInfoDto> GetLastMembershipInfoWithCalculations(int memberId, int companyId);

    }
}
