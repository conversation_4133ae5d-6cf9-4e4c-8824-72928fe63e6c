using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class MembershipFreezeHistoryManager : IMembershipFreezeHistoryService
    {
        private readonly IMembershipFreezeHistoryDal _membershipFreezeHistoryDal;
        private const int MAX_FREEZE_DAYS_PER_YEAR = 365;

        public MembershipFreezeHistoryManager(IMembershipFreezeHistoryDal membershipFreezeHistoryDal)
        {
            _membershipFreezeHistoryDal = membershipFreezeHistoryDal;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "MembershipFreezeHistory", "All")]
        public IDataResult<List<MembershipFreezeHistoryDto>> GetAll()
        {
            var result = _membershipFreezeHistoryDal.GetFreezeHistoryDetails();
            return new SuccessDataResult<List<MembershipFreezeHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "MembershipFreezeHistory", "Membership")]
        public IDataResult<List<MembershipFreezeHistoryDto>> GetByMembershipId(int membershipId)
        {
            var result = _membershipFreezeHistoryDal.GetFreezeHistoryByMembershipId(membershipId);
            return new SuccessDataResult<List<MembershipFreezeHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("MembershipFreezeHistory")]
        public IResult Add(MembershipFreezeHistory history)
        {
            _membershipFreezeHistoryDal.Add(history);
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("MembershipFreezeHistory")]
        public IResult Update(MembershipFreezeHistory history)
        {
            _membershipFreezeHistoryDal.Update(history);
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "MembershipFreezeHistory", "RemainingDays")]
        public IDataResult<int> GetRemainingFreezeDays(int membershipId)
        {
            var usedDays = _membershipFreezeHistoryDal.GetTotalFreezeDaysUsedInLastYear(membershipId);
            var remainingDays = MAX_FREEZE_DAYS_PER_YEAR - usedDays;
            return new SuccessDataResult<int>(remainingDays >= 0 ? remainingDays : 0);
        }
    }
}