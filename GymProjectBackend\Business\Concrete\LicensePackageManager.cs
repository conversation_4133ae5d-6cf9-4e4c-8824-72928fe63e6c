﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicensePackageManager : ILicensePackageService
    {
        private readonly ILicensePackageDal _licensePackageDal;

        public LicensePackageManager(ILicensePackageDal licensePackageDal)
        {
            _licensePackageDal = licensePackageDal;
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("LicensePackage")]
        public IResult Add(LicensePackage licensePackage)
        {
            return _licensePackageDal.AddLicensePackage(licensePackage);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("LicensePackage")]
        public IResult Delete(int id)
        {
            _licensePackageDal.Delete(id);
            return new SuccessResult("Lisans paketi başarıyla silindi");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "LicensePackage", "Master")]
        public IDataResult<List<LicensePackage>> GetAll()
        {
            return new SuccessDataResult<List<LicensePackage>>(_licensePackageDal.GetAll(lp => lp.IsActive == true));
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "LicensePackage", "Details")]
        public IDataResult<LicensePackage> GetById(int id)
        {
            return new SuccessDataResult<LicensePackage>(_licensePackageDal.Get(lp => lp.LicensePackageID == id));
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("LicensePackage")]
        public IResult Update(LicensePackage licensePackage)
        {
            return _licensePackageDal.UpdateLicensePackage(licensePackage);
        }
    }
}